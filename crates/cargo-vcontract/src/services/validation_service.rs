use regex::Regex;
use serde_json::Value;
use std::path::Path;

use super::error_service::{ServiceError, ServiceResult};
use crate::cmd::utils::{
    display_function_arguments, parse_args_with_metadata, parse_contract_functions,
    parse_json_args, validate_function_call,
};

/// Validation service handles all parameter validation and type conversion
#[derive(Debu<PERSON>, Clone)]
pub struct ValidationService;

#[allow(dead_code)]
impl ValidationService {
    /// Create a new instance of ValidationService
    pub fn new() -> Self {
        Self
    }

    /// Parse arguments with type awareness based on user intention
    pub fn parse_arguments_with_type_awareness(
        &self,
        args: &[crate::cmd::deploy::ArgValue],
    ) -> ServiceResult<Vec<Value>> {
        let mut parsed_args = Vec::new();

        for arg_value in args {
            let value = match arg_value {
                crate::cmd::deploy::ArgValue::ExplicitString(s) => {
                    // User explicitly wants a string
                    Value::String(s.clone())
                }
                crate::cmd::deploy::ArgValue::Auto(s) => {
                    // Use smart parsing logic
                    use crate::cmd::utils::parse_smart_arg;
                    parse_smart_arg(s)
                        .map_err(|e| ServiceError::validation(e.to_string()))?
                }
            };
            parsed_args.push(value);
        }

        Ok(parsed_args)
    }

    /// Validate function call parameters
    pub fn validate_function_call(
        &self,
        function_name: &str,
        args: &[Value],
        is_call: bool,
        output_json: bool,
    ) -> ServiceResult<()> {
        // First check if function name is empty (add this check that was missing)
        if function_name.is_empty() {
            return Err(ServiceError::validation(
                "Function name cannot be empty".to_string(),
            ));
        }

        validate_function_call(function_name, args, is_call, output_json)
            .map_err(|e| ServiceError::validation(e.to_string()))
    }

    /// Validate types and convert if necessary
    pub fn validate_types(
        &self,
        args: &[String],
        contract_file: &Path,
        is_constructor: bool,
        function_name: Option<&str>,
        output_json: bool,
    ) -> ServiceResult<Vec<Value>> {
        let validated_args = parse_args_with_metadata(
            args,
            contract_file,
            is_constructor,
            function_name,
            output_json,
        )
        .map_err(|e| ServiceError::validation(e.to_string()))?;

        // Display function arguments after validation
        if !output_json && !args.is_empty() && !is_constructor {
            if let Some(fname) = function_name {
                if let Ok(contract_address) = std::env::var("VCONTRACT_CURRENT_ADDRESS") {
                    let _ = display_function_arguments(
                        &contract_address,
                        fname,
                        &validated_args,
                        true,
                        false,
                    );
                }
            }
        }

        Ok(validated_args)
    }

    /// Validate JSON arguments format
    pub fn validate_json_args(&self, args: &[String]) -> ServiceResult<Vec<Value>> {
        // Check for clearly invalid JSON before delegating to parse_json_args
        for arg in args {
            if arg.contains("invalid_json{")
                || arg.ends_with('{') && !arg.starts_with('{')
            {
                return Err(ServiceError::validation("Invalid JSON format".to_string()));
            }
        }

        parse_json_args(args).map_err(|e| {
            let error_msg = e.to_string();
            if error_msg.contains("expected") || error_msg.contains("JSON") {
                ServiceError::validation("Invalid JSON format".to_string())
            } else {
                ServiceError::validation(error_msg)
            }
        })
    }

    /// Check if function is readonly
    pub fn is_readonly_function(
        &self,
        contract_file: &Path,
        function_name: &str,
    ) -> ServiceResult<bool> {
        // Use metadata parsing to determine if function is readonly
        match parse_contract_functions(contract_file) {
            Ok(functions) => {
                for function in functions {
                    if function.name == function_name {
                        // Check if function is marked as readonly in metadata
                        // For now, assume functions starting with "get_" or "query_" are readonly
                        let is_readonly = function_name.starts_with("get_")
                            || function_name.starts_with("query_")
                            || function_name.starts_with("view_");
                        return Ok(is_readonly);
                    }
                }
                // Function not found, assume not readonly
                Ok(false)
            }
            Err(_) => {
                // If we can't parse metadata, use naming convention
                let is_readonly = function_name.starts_with("get_")
                    || function_name.starts_with("query_")
                    || function_name.starts_with("view_");
                Ok(is_readonly)
            }
        }
    }

    /// Validate Ethereum-style addresses
    pub fn validate_addresses(&self, addresses: &[String]) -> ServiceResult<()> {
        let address_regex = Regex::new(r"^0x[0-9a-fA-F]{40}$").unwrap();

        for address in addresses {
            if !address_regex.is_match(address) {
                return Err(ServiceError::validation(format!(
                    "Invalid address format: {}",
                    address
                )));
            }
        }

        Ok(())
    }

    /// Validate fuel amount
    pub fn validate_fuel(&self, fuel: u64) -> ServiceResult<()> {
        if fuel == 0 {
            return Err(ServiceError::validation(
                "Fuel must be greater than 0".to_string(),
            ));
        }
        Ok(())
    }

    /// Validate node URL format
    pub fn validate_node_url(&self, url: &str) -> ServiceResult<()> {
        if url.is_empty() {
            return Err(ServiceError::validation(
                "Node URL cannot be empty".to_string(),
            ));
        }

        // Basic URL format validation
        let parts: Vec<&str> = url.split(':').collect();
        if parts.len() != 2 {
            return Err(ServiceError::validation("Invalid URL format".to_string()));
        }

        // Validate port
        if let Err(_) = parts[1].parse::<u16>() {
            return Err(ServiceError::validation("Invalid port number".to_string()));
        }

        Ok(())
    }

    /// Validate contract file
    pub fn validate_contract_file(&self, path: &Path) -> ServiceResult<()> {
        if !path.exists() {
            return Err(ServiceError::validation(
                "Contract file does not exist".to_string(),
            ));
        }

        if let Some(extension) = path.extension() {
            if extension != "wasm" {
                return Err(ServiceError::validation(
                    "Contract file must have .wasm extension".to_string(),
                ));
            }
        } else {
            return Err(ServiceError::validation(
                "Contract file must have .wasm extension".to_string(),
            ));
        }

        Ok(())
    }
}

impl Default for ValidationService {
    fn default() -> Self {
        Self::new()
    }
}
