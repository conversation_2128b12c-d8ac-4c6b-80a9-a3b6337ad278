// Services module for cargo-vcontract
// Contains business logic services separated from CLI commands

// Error types module
pub mod error_service;

// Service modules
pub mod auth_service;
pub mod config_service;
pub mod metadata_service;
pub mod network_service;
pub mod validation_service;

// Export error types
pub use error_service::{ServiceError, ServiceResult};

// Test modules
#[cfg(test)]
mod tests;

// Export service types
pub use auth_service::AuthService;
pub use config_service::ConfigService;
pub use metadata_service::MetadataService;
pub use network_service::NetworkService;
// pub use parsing_service::ParsingService;
pub use validation_service::ValidationService;

// Only re-export these types when they are actually used elsewhere
// Keeping them commented as reference in case they are needed in the future
// pub use config_service::VGraphConfig;
// pub use metadata_service::{ConstructorParam, ContractFunction, ContractMetadata, FunctionParam};
// pub use network_service::{ContractFileType, ContractSearchConfig, NetworkStatus, SearchStrategy, TransactionConfirmation};
// pub use parsing_service::ValidationResult;
