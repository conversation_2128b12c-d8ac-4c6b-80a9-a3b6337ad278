use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;

use super::error_service::{ServiceError, ServiceResult};

/// VGraph configuration structure
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct VGraphConfig {
    pub default_node: String,
}

impl Default for VGraphConfig {
    fn default() -> Self {
        Self {
            default_node: "127.0.0.1:9877".to_string(),
        }
    }
}

/// Configuration service handles all configuration-related operations
#[derive(Debug, Clone)]
pub struct ConfigService;

impl ConfigService {
    /// Create a new instance of ConfigService
    pub fn new() -> Self {
        Self
    }

    /// Get the VGraph configuration directory
    pub fn get_vgraph_config_dir(&self) -> ServiceResult<PathBuf> {
        let home_dir = dirs::home_dir()
            .ok_or_else(|| ServiceError::internal("Unable to get home directory"))?;
        Ok(home_dir.join(".vgraph"))
    }

    /// Get the path to the configuration file
    pub fn get_config_file_path(&self) -> ServiceResult<PathBuf> {
        Ok(self.get_vgraph_config_dir()?.join("config.json"))
    }

    /// Load configuration from file
    pub fn load_config(&self) -> ServiceResult<VGraphConfig> {
        let config_path = self.get_config_file_path()?;

        if !config_path.exists() {
            return Ok(VGraphConfig::default());
        }

        let content = fs::read_to_string(&config_path).map_err(|e| {
            ServiceError::internal(format!("Failed to read config file: {}", e))
        })?;

        let config: VGraphConfig = serde_json::from_str(&content).map_err(|e| {
            ServiceError::internal(format!("Failed to parse config file: {}", e))
        })?;

        Ok(config)
    }

    /// Save configuration to file
    pub fn save_config(&self, config: &VGraphConfig) -> ServiceResult<()> {
        let config_dir = self.get_vgraph_config_dir()?;
        if !config_dir.exists() {
            fs::create_dir_all(&config_dir).map_err(|e| {
                ServiceError::internal(format!(
                    "Failed to create config directory: {}",
                    e
                ))
            })?;
        }

        let config_path = self.get_config_file_path()?;
        let content = serde_json::to_string_pretty(config).map_err(|e| {
            ServiceError::internal(format!("Failed to serialize config: {}", e))
        })?;

        fs::write(&config_path, content).map_err(|e| {
            ServiceError::internal(format!("Failed to write config file: {}", e))
        })?;

        Ok(())
    }

    /// Get node URL from command parameter, config file, or default
    pub fn get_node_url(&self, cmd_node: &str) -> ServiceResult<String> {
        if !cmd_node.is_empty() {
            return Ok(cmd_node.to_string());
        }

        // Try to load from config
        match self.load_config() {
            Ok(config) => Ok(config.default_node),
            Err(_) => Ok("127.0.0.1:9877".to_string()),
        }
    }

    /// Set default node URL
    pub fn set_default_node(&self, url: String) -> ServiceResult<()> {
        let mut config = self.load_config().unwrap_or_default();
        config.default_node = url;
        self.save_config(&config)
    }

    /// Reset configuration to defaults
    pub fn reset_config(&self) -> ServiceResult<VGraphConfig> {
        let default_config = VGraphConfig::default();
        self.save_config(&default_config)?;
        Ok(default_config)
    }
}

impl Default for ConfigService {
    fn default() -> Self {
        Self::new()
    }
}
