use serde::{Deserialize, Serialize};
use serde_json::Value;
use vgraph_sdk_core::rpc::vgraph_client::VGraphClient;

use super::error_service::{ServiceError, ServiceResult};

/// Network status information
#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NetworkStatus {
    pub connected: bool,
    pub latest_block: u64,
    pub node_version: String,
}

/// Transaction confirmation result
#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct TransactionConfirmation {
    pub transaction_hash: String,
    pub receipt: Value,
    pub confirmed: bool,
}

#[derive(Debug, Clone)]
pub struct NetworkService;

impl NetworkService {
    /// Create a new instance of NetworkService
    pub fn new() -> Self {
        Self
    }

    /// Connect to VGraph node
    pub async fn connect_to_node(&self, url: &str) -> ServiceResult<VGraphClient> {
        let url = if url.is_empty() {
            "127.0.0.1:9877"
        } else {
            url
        };

        VGraphClient::connect(url).await.map_err(|e| {
            ServiceError::network(format!("Failed to connect to node {}: {}", url, e))
        })
    }

    /// Connect to VGraph node with output control (for backward compatibility)
    #[allow(dead_code)]
    pub async fn connect_to_node_with_output(
        &self,
        url: &str,
        _output_json: bool,
    ) -> ServiceResult<VGraphClient> {
        // We no longer handle UI output in the service layer
        // The cmd layer should handle output formatting
        self.connect_to_node(url).await
    }

    /// Check network status
    #[allow(dead_code)]
    pub async fn check_network_status(
        &self,
        client: &VGraphClient,
    ) -> ServiceResult<NetworkStatus> {
        let block_value = client.get_best_block().await.map_err(|e| {
            ServiceError::network(format!("Failed to get latest block number: {}", e))
        })?;

        let latest_block = match block_value.as_u64() {
            Some(num) => num,
            None => {
                return Err(ServiceError::network(
                    "Invalid block number format".to_string(),
                ))
            }
        };

        // Since get_node_version doesn't exist, we'll use a placeholder version
        let node_version = "v1.0.0".to_string();

        Ok(NetworkStatus {
            connected: true,
            latest_block,
            node_version,
        })
    }

    /// Wait for transaction confirmation
    #[allow(dead_code)]
    pub async fn wait_for_confirmation(
        &self,
        client: &VGraphClient,
        tx_hash: &str,
    ) -> ServiceResult<TransactionConfirmation> {
        loop {
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

            match client.get_transaction_receipt(tx_hash).await {
                Ok(receipt) => {
                    // Check if transaction is still pending
                    if let Some(message) = receipt.get("message") {
                        if message.as_str().map_or(false, |s| s.contains("pending")) {
                            continue; // Still waiting for confirmation
                        }
                    }

                    return Ok(TransactionConfirmation {
                        transaction_hash: tx_hash.to_string(),
                        receipt,
                        confirmed: true,
                    });
                }
                Err(_) => {
                    // If we get an error, it might just mean the transaction is still being processed
                    continue;
                }
            }
        }
    }
}

impl Default for NetworkService {
    fn default() -> Self {
        Self::new()
    }
}
