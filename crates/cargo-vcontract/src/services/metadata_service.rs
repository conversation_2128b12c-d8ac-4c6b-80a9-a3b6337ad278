use serde_json::Value;
use std::fs;
use std::path::Path;

use super::error_service::{ServiceError, ServiceResult};

/// Metadata service handles all contract metadata parsing and processing
#[derive(Debug, Clone)]
pub struct MetadataService;

impl MetadataService {
    /// Create a new instance of MetadataService
    pub fn new() -> Self {
        Self
    }

    /// Find contract file for deployment
    #[allow(dead_code)]
    pub fn find_contract_file_for_deploy(&self) -> ServiceResult<std::path::PathBuf> {
        use crate::cmd::utils::ContractDiscoveryService;
        ContractDiscoveryService::find_for_deploy(false)
            .map_err(|e| ServiceError::file_system(e.to_string()))
    }

    /// Find contract file for deploy with verbose output
    pub fn find_contract_file_for_deploy_verbose(
        &self,
        verbose: bool,
    ) -> ServiceResult<std::path::PathBuf> {
        use crate::cmd::utils::ContractDiscoveryService;
        ContractDiscoveryService::find_for_deploy(verbose)
            .map_err(|e| ServiceError::file_system(e.to_string()))
    }

    /// Parse contract file and extract WASM bytecode
    pub fn parse_contract_file(&self, file_path: &Path) -> ServiceResult<Vec<u8>> {
        let content = fs::read_to_string(file_path).map_err(|e| {
            ServiceError::internal(format!("Failed to read contract file: {}", e))
        })?;

        let contract_json: Value = serde_json::from_str(&content).map_err(|e| {
            ServiceError::internal(format!("Failed to parse contract JSON: {}", e))
        })?;

        let source = contract_json.get("source").ok_or_else(|| {
            ServiceError::validation("Contract file missing 'source' field".to_string())
        })?;

        let wasm_hex = source.get("wasm").and_then(|w| w.as_str()).ok_or_else(|| {
            ServiceError::validation("Contract file missing WASM data".to_string())
        })?;

        // Remove 0x prefix if present
        let hex_str = if wasm_hex.starts_with("0x") {
            &wasm_hex[2..]
        } else {
            wasm_hex
        };

        hex::decode(hex_str).map_err(|e| {
            ServiceError::internal(format!("Failed to decode WASM hex: {}", e))
        })
    }
}

impl Default for MetadataService {
    fn default() -> Self {
        Self::new()
    }
}
