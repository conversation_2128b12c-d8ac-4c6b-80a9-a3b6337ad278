
use clap::Args;
use colored::Colorize;
use contract_build::VerbosityFlags;
use serde_json::json;

use std::convert::TryFrom;

use std::path::PathBuf;
use tokio::runtime::Runtime;

use crate::cmd::utils::{
    format_output, wait_for_confirmation,
};
use crate::services::{
    AuthService, ConfigService, MetadataService, NetworkService, ServiceError, ServiceResult,
    ValidationService,
};

/// Upgrade an existing upgradable smart contract
#[derive(Debug, Args)]
#[clap(name = "upgrade")]
pub struct UpgradeCommand {
    /// Address of the contract to upgrade
    #[clap(value_parser)]
    contract_address: String,

    /// Path to the new contract file (.wasm or .contract file)
    #[clap(value_parser)]
    contract_file: Option<PathBuf>,

    /// Node URL (override config file setting - currently configured: use 'cargo vcontract config' to view/change)
    #[clap(long)]
    node: Option<String>,

    /// Source code URL
    #[clap(long, default_value = "")]
    source_url: String,

    /// Git commit hash
    #[clap(long, default_value = "")]
    git_hash: String,

    /// Use reproducible build
    #[clap(long, default_value = "true")]
    reproducible: bool,

    /// Fuel value
    #[clap(long, default_value = "1000000")]
    fuel: u64,

    /// Verbosity flags
    #[clap(flatten)]
    verbosity: VerbosityFlags,
}

impl UpgradeCommand {
    /// Check if JSON output is requested
    pub fn is_json(&self) -> bool {
        self.verbosity.is_json()
    }

    pub fn exec(&self) -> ServiceResult<()> {
        // Create tokio runtime
        let runtime = Runtime::new()
            .map_err(|e| ServiceError::internal(format!("Failed to create tokio runtime: {}", e)))?;
        runtime.block_on(self.async_exec())
    }

    async fn async_exec(&self) -> ServiceResult<()> {
        // Initialize services
        let config_service = ConfigService::new();
        let validation_service = ValidationService::new();
        let metadata_service = MetadataService::new();
        let network_service = NetworkService::new();
        let auth_service = AuthService::new();

        // Parse verbosity settings
        let verbosity = contract_build::Verbosity::try_from(&self.verbosity)
            .map_err(|e| ServiceError::internal(e.to_string()))?;
        let verbose = verbosity.is_verbose();
        let is_json = verbosity.is_json();

        // Step 1: Validate fuel parameter
        validation_service.validate_fuel(self.fuel)?;

        // Step 2: Find contract file
        let contract_file = match &self.contract_file {
            Some(path) => path.clone(),
            None => metadata_service.find_contract_file_for_deploy_verbose(verbose)?,
        };

        // Step 3: Get node URL from configuration
        let node_url = config_service
            .get_node_url(self.node.as_deref().unwrap_or("127.0.0.1:9877"))?;

        // Step 4: Connect to network
        let client = network_service.connect_to_node(&node_url).await?;

        // Step 5: Read contract file
        if verbose && !is_json {
            println!(
                "Reading new contract file: {}...",
                contract_file.display().to_string().blue()
            );
        }

        let contract_data =
            if contract_file.extension().and_then(|ext| ext.to_str()) == Some("wasm") {
                // Read .wasm file directly
                std::fs::read(&contract_file).map_err(|e| {
                    ServiceError::file_system(format!(
                        "Failed to read WASM file {}: {}",
                        contract_file.display(),
                        e
                    ))
                })?
            } else {
                // Use contract file parser for .contract files
                metadata_service.parse_contract_file(&contract_file)?
            };

        // Step 6: Get authentication credentials
        let private_key = auth_service.get_saved_private_key()
            .map_err(|e| ServiceError::auth(e.to_string()))?;

        // Step 7: Build and submit transaction
        if verbose && !is_json {
            println!("{}", "Submitting upgrade transaction to network...".blue());
            println!(
                "{} Using fuel: {}",
                "[FUEL]".yellow(),
                self.fuel.to_string().green()
            );
        }

        // Ensure contract address has correct format
        let contract_address = if !self.contract_address.starts_with("0x") {
            format!("0x{}", self.contract_address)
        } else {
            self.contract_address.clone()
        };

        let contract_hex = hex::encode(&contract_data);

        let params = json!([{
            "contract_address": contract_address,
            "contract_hex_bytecode": contract_hex,
            "contract_source_url": self.source_url.clone(),
            "git_commit_hash": self.git_hash.clone(),
            "reproducible_build": self.reproducible,
            "fuel": self.fuel,
            "privatekey": private_key
        }]);

        let response = match client.request("contract.upgrade", Some(params)).await {
            Ok(response) => response,
            Err(e) => {
                return Err(ServiceError::network(format!("RPC request failed: {}", e)));
            }
        };

        let tx_hash = match response.get("transaction_hash").and_then(|v| v.as_str()) {
            Some(hash) => hash,
            None => {
                return Err(ServiceError::network(format!(
                    "Invalid transaction hash response. Full response: {}",
                    response
                )));
            }
        };

        // Step 8: Wait for transaction confirmation
        let receipt = wait_for_confirmation(&client, tx_hash, !is_json).await?;

        // Step 9: Process and display results
        let success = receipt["receipt"]["status"].as_bool().unwrap_or(false);
        let status = if success { "success" } else { "failed" };

        let fuel_consumed = receipt
            .get("receipt")
            .and_then(|r| r.get("logs"))
            .and_then(|logs| logs.as_array())
            .and_then(|logs| {
                for log in logs {
                    if let Some(log_str) = log.as_str() {
                        if log_str.contains("Fuel consumed:") {
                            if let Some(fuel_part) =
                                log_str.split("Fuel consumed:").nth(1)
                            {
                                if let Ok(fuel_val) = fuel_part.trim().parse::<u64>() {
                                    return Some(fuel_val);
                                }
                            }
                        }
                    }
                }
                None
            })
            .unwrap_or(0);

        let result = json!({
            "transaction_hash": tx_hash,
            "contract_address": contract_address,
            "status": status,
            "success": success,
            "receipt": receipt
        });

        if is_json {
            println!("{}", format_output(result, true));
        } else {
            println!("\n{}: {}", "Transaction Hash".green().bold(), tx_hash);
            println!(
                "{}: {}",
                "Contract Address".green().bold(),
                contract_address
            );
            println!("{}: {}", "Upgrade Status".green().bold(), status);
            if fuel_consumed > 0 {
                println!(
                    "{} {}: {}",
                    "[GAS]".yellow(),
                    "Fuel Consumed".green().bold(),
                    fuel_consumed.to_string().yellow()
                );
            }

            if success {
                if verbose {
                    println!("{}:", "Receipt".green().bold());
                    let pretty_receipt = serde_json::to_string_pretty(&receipt)
                        .unwrap_or_else(|_| receipt.to_string());
                    println!("{}", pretty_receipt);
                }

                println!("\n{}", "Contract upgrade successful!".green().bold());
            } else {
                println!("\n{}", "Contract upgrade failed!".red().bold());

                let pretty_receipt = serde_json::to_string_pretty(&receipt)
                    .unwrap_or_else(|_| receipt.to_string());

                println!(
                    "{}: {}",
                    "Error Details".red().bold(),
                    format!("Contract upgrade failed. Full receipt:\n{}", pretty_receipt)
                );
            }
        }

        Ok(())
    }
}
