
use crate::services::{ServiceError, ServiceResult};
use clap::{Args, Subcommand};
use colored::Colorize;

use crate::cmd::auth::{get_saved_address, is_logged_in};
use crate::services::ConfigService;

/// Manage VGraph configuration
#[derive(Debug, Args)]
#[clap(name = "config")]
pub struct ConfigCommand {
    #[clap(subcommand)]
    subcmd: ConfigSubcommand,
}

#[derive(Debug, Subcommand)]
enum ConfigSubcommand {
    /// Set default node URL
    #[clap(name = "set-node")]
    SetNode {
        /// Node URL (e.g., 127.0.0.1:9877)
        #[clap(value_parser)]
        url: String,
    },
    /// Show current configuration
    #[clap(name = "show")]
    Show,
    /// Reset configuration to defaults
    #[clap(name = "reset")]
    Reset,
}

impl ConfigCommand {
    pub fn exec(&self) -> ServiceResult<()> {
        let config_service = ConfigService::new();

        match &self.subcmd {
            ConfigSubcommand::SetNode { url } => {
                config_service
                    .set_default_node(url.clone())
                    .map_err(|e| ServiceError::internal(format!("Failed to set node URL: {}", e)))?;

                println!("{}: {}", "Default node set to".green().bold(), url.yellow());
                println!(
                    "Configuration saved to: {}",
                    config_service
                        .get_config_file_path()
                        .map_err(|e| ServiceError::internal(format!("Failed to get config path: {}", e)))?
                        .display()
                );
                Ok(())
            }
            ConfigSubcommand::Show => {
                let config = config_service
                    .load_config()
                    .map_err(|e| ServiceError::internal(format!("Failed to load config: {}", e)))?;

                println!("{}", "Current VGraph Configuration:".green().bold());
                println!(
                    "  {}: {}",
                    "Default Node".blue(),
                    config.default_node.yellow()
                );
                println!(
                    "  {}: {}",
                    "Config File".blue(),
                    config_service
                        .get_config_file_path()
                        .map_err(|e| ServiceError::internal(format!("Failed to get config path: {}", e)))?
                        .display()
                        .to_string()
                        .yellow()
                );

                // Show authentication status
                if is_logged_in() {
                    if let Ok(address) = get_saved_address() {
                        println!(
                            "  {}: {} [OK]",
                            "Authentication".blue(),
                            address.yellow()
                        );
                    } else {
                        println!(
                            "  {}: {}",
                            "Authentication".blue(),
                            "Yes (address unavailable)".yellow()
                        );
                    }
                } else {
                    println!("  {}: {}", "Authentication".blue(), "No [FAIL]".red());
                    println!("\n{}: Use 'cargo vcontract key generate <n>' to create your first key", "Hint".yellow().bold());
                }
                Ok(())
            }
            ConfigSubcommand::Reset => {
                let default_config = config_service.reset_config()?;

                println!("{}", "Configuration reset to defaults:".green().bold());
                println!(
                    "  {}: {}",
                    "Default Node".blue(),
                    default_config.default_node.yellow()
                );
                println!("\n{}: {}", "Note".yellow().bold(), "Authentication key was not affected. Use 'cargo vcontract key remove <n>' to remove saved credentials.".italic());
                Ok(())
            }
        }
    }
}

/// Get node URL, with fallback to config and default
/// This function is kept for backward compatibility
pub fn get_node_url(cmd_node: &str) -> ServiceResult<String> {
    let config_service = ConfigService::new();
    config_service.get_node_url(cmd_node)
}
