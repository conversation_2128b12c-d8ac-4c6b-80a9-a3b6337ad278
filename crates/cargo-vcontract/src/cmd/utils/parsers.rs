// Argument parsing functionality for cargo-vcontract
// This module handles parsing, conversion, and validation of command-line arguments

use anyhow::{anyhow, Result};
use colored::Colorize;
use serde_json::Value;
use std::fs;
use std::path::Path;

use super::metadata::{parse_contract_functions, ConstructorParam, ContractFunction};

/// Parse JSON arguments with smart type detection
pub fn parse_json_args(args: &[String]) -> Result<Vec<Value>> {
    let mut parsed_args = Vec::new();
    for arg in args {
        let value = parse_smart_arg(arg)?;
        parsed_args.push(value);
    }
    Ok(parsed_args)
}

/// Parse arguments with type awareness based on contract metadata
pub fn parse_args_with_metadata(
    args: &[String],
    contract_file: &Path,
    is_constructor: bool,
    function_name: Option<&str>,
    output_json: bool,
) -> Result<Vec<Value>> {
    // Try to get type information from contract metadata
    match get_parameter_types(contract_file, is_constructor, function_name) {
        Ok(param_types) => {
            if args.len() != param_types.len() {
                // Print function signature for user guidance (only in non-JSON mode)
                if !output_json {
                    print_function_signature_with_metadata(
                        contract_file,
                        is_constructor,
                        function_name,
                        output_json,
                    );
                }
                return Err(anyhow!(
                    "Parameter count mismatch: expected {}, got {}",
                    param_types.len(),
                    args.len()
                ));
            }

            let mut parsed_args = Vec::new();
            for (arg, expected_type) in args.iter().zip(param_types.iter()) {
                // First apply type conversion rules (e.g., 100i32 -> Number(100))
                let pre_parsed = parse_smart_arg(arg)?;

                // Then apply type-aware conversion if needed
                let value = convert_to_expected_type(pre_parsed, expected_type)?;
                parsed_args.push(value);
            }
            Ok(parsed_args)
        }
        Err(e) => {
            // Check if this is a function not found error
            let error_msg = e.to_string();
            if error_msg.contains("not found in contract metadata") {
                // This is a function validation error, don't fallback
                return Err(e);
            }
            // For other metadata parsing errors, fallback to smart parsing
            parse_json_args(args)
        }
    }
}

/// Parse a single argument using type interpretation
/// Now supports Rust-style type literals: 42u8, true, "string", 0x123, etc.
pub fn parse_smart_arg(arg: &str) -> Result<Value> {
    match parse_typed_value(arg) {
        Ok(value) => Ok(value),
        Err(_error) => {
            // Parsing failed, fall back to string
            Ok(Value::String(arg.to_string()))
        }
    }
}

/// Validate constructor arguments and provide helpful error messages
pub fn validate_constructor_args(
    args: &[Value],
    contract_file: &Path,
    verbose: bool,
    output_json: bool,
) -> Result<()> {
    // Parse constructor metadata from .contract file
    let constructor_params = parse_constructor_metadata(contract_file)?;

    // Check if arguments are provided when required
    if constructor_params.is_empty() && !args.is_empty() {
        return Err(anyhow!(
            "{}

{}

{}",
            "Constructor does not accept any parameters, but arguments were provided!"
                .red()
                .bold(),
            format!("Provided {} arguments:", args.len()).yellow(),
            "Please remove all --args parameters.".green()
        ));
    }

    if !constructor_params.is_empty() && args.is_empty() {
        let mut error_msg = format!(
            "{}

{}:",
            "No constructor arguments provided, but the contract constructor requires parameters!".red().bold(),
            "Required parameters".yellow()
        );

        for (i, param) in constructor_params.iter().enumerate() {
            error_msg.push_str(&format!(
                "\n  {}: {} ({})",
                i + 1,
                param.label.blue(),
                param.type_name
            ));
        }

        error_msg.push_str(&format!(
            "\n\n{}",
            "Usage: cargo vcontract deploy --args '<value1>' --args '<value2>' ..."
                .green()
        ));

        error_msg.push_str(&format!(
            "\n{}",
            "Example: cargo vcontract deploy --args '\"Hello\"' --args '42'"
        ));

        return Err(anyhow!(error_msg));
    }

    // Check argument count
    if args.len() != constructor_params.len() {
        let mut error_msg = format!(
            "{}

{}: {}
{}: {}",
            "Argument count mismatch!".red().bold(),
            "Expected args".yellow(),
            constructor_params.len(),
            "Provided args".yellow(),
            args.len()
        );

        if !constructor_params.is_empty() {
            error_msg.push_str(&format!("\n\n{}:", "Required parameters".yellow()));

            for (i, param) in constructor_params.iter().enumerate() {
                error_msg.push_str(&format!(
                    "\n  {}: {} ({})",
                    i + 1,
                    param.label.blue(),
                    param.type_name
                ));
            }
        }

        return Err(anyhow!(error_msg));
    }

    if verbose && !output_json && !args.is_empty() {
        println!(
            "{}: {} arguments",
            "Using constructor arguments".blue(),
            args.len()
        );
        for (i, (param, value)) in constructor_params.iter().zip(args.iter()).enumerate()
        {
            println!(
                "  {}: {} = {}",
                i + 1,
                param.label.blue(),
                value.to_string().green()
            );
        }
    }

    Ok(())
}

/// Validate function calls and provide helpful error messages
pub fn validate_function_call(
    function_name: &str,
    args: &[Value],
    is_call: bool,
    output_json: bool,
) -> Result<()> {
    // Try to find contract files in common locations
    let current_dir = std::env::current_dir()?;
    let search_dirs = vec![
        current_dir.clone(),
        current_dir.join("target").join("contract"),
        current_dir.join("target").join("glue"),
    ];

    let mut found_function: Option<ContractFunction> = None;
    let mut contract_file_found = false;

    for search_dir in search_dirs {
        if let Ok(entries) = std::fs::read_dir(&search_dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                if path.extension().and_then(|ext| ext.to_str()) == Some("contract") {
                    contract_file_found = true;
                    if let Ok(functions) = parse_contract_functions(&path) {
                        for func in functions {
                            if func.name == function_name {
                                found_function = Some(func);
                                break;
                            }
                        }
                        if found_function.is_some() {
                            break;
                        }
                    }
                }
            }
            if found_function.is_some() {
                break;
            }
        }
    }

    let func = match found_function {
        Some(f) => f,
        None => {
            if contract_file_found {
                let suggested_cmd = if is_call { "call" } else { "query" };

                if output_json {
                    // JSON mode: structured error without colors or newlines
                    return Err(anyhow!(
                        "Function '{}' not found in contract. Please check the function name or run 'cargo vcontract {} ls' to see available functions.",
                        function_name,
                        suggested_cmd
                    ));
                } else {
                    // Normal mode: with colors
                    return Err(anyhow!(
                        "{}

{}: {}

{}",
                        "Function not found in contract!".red().bold(),
                        "Function name".yellow(),
                        function_name.blue(),
                        format!("Please check the function name or run 'cargo vcontract {} ls' to see available functions.", suggested_cmd).green()
                    ));
                }
            } else {
                // No contract file found, just proceed (backward compatibility)
                return Ok(());
            }
        }
    };

    // Validate function type (readonly vs atomic) - but only show warning if not in JSON mode
    if !output_json {
        if is_call && func.is_readonly {
            println!(
                "{}

{}: {}
{}: {}

{}",
                "[WARNING]  Function Type Mismatch Warning".yellow().bold(),
                "Function".yellow(),
                function_name.blue(),
                "Type".yellow(),
                "Readonly (Query function)".blue(),
                "[TIP] Consider using 'cargo vcontract query' instead for better performance."
                    .green()
            );
        } else if !is_call && !func.is_readonly {
            println!(
                "{}

{}: {}
{}: {}

{}",
                "[WARNING]  Function Type Mismatch Warning".yellow().bold(),
                "Function".yellow(),
                function_name.blue(),
                "Type".yellow(),
                "Atomic (State-changing function)".blue(),
                "[TIP] Note: Use 'cargo vcontract call' to execute state-changing functions."
                    .green()
            );
        }
    }

    // Validate argument count
    if args.len() != func.parameters.len() {
        let error_msg = if output_json {
            format!(
                "Argument count mismatch!\n\nFunction: {}\nExpected args: {}\nProvided args: {}\n\nRequired parameters:",
                function_name,
                func.parameters.len(),
                args.len()
            )
        } else {
            format!(
                "{}

{}: {}
{}: {}
{}: {}

{}:",
                "Argument count mismatch!".red().bold(),
                "Function".yellow(),
                function_name.blue(),
                "Expected args".yellow(),
                func.parameters.len(),
                "Provided args".yellow(),
                args.len(),
                "Required parameters".yellow()
            )
        };

        let mut full_error_msg = error_msg;

        for (i, param) in func.parameters.iter().enumerate() {
            if output_json {
                full_error_msg.push_str(&format!(
                    "\n  {}: {} ({})",
                    i + 1,
                    param.label,
                    param.type_name
                ));
            } else {
                full_error_msg.push_str(&format!(
                    "\n  {}: {} ({})",
                    i + 1,
                    param.label.blue(),
                    param.type_name.green()
                ));
            }
        }

        if !func.parameters.is_empty() {
            let usage_line = format!(
                "Usage: cargo vcontract {} {} {}",
                if is_call { "call" } else { "query" },
                "<contract_address>",
                function_name
            );

            if output_json {
                full_error_msg.push_str(&format!("\n\n{}", usage_line));
            } else {
                full_error_msg.push_str(&format!("\n\n{}", usage_line.green()));
            }

            for param in &func.parameters {
                full_error_msg.push_str(&format!(" --args '<{}>>'", param.label));
            }
        }

        return Err(anyhow!(full_error_msg));
    }

    Ok(())
}

// Helper functions

/// Get parameter types from contract metadata
fn get_parameter_types(
    contract_file: &Path,
    is_constructor: bool,
    function_name: Option<&str>,
) -> Result<Vec<String>> {
    let content = fs::read_to_string(contract_file)
        .map_err(|e| anyhow!("Failed to read contract file: {}", e))?;

    let contract_data: Value = serde_json::from_str(&content)
        .map_err(|e| anyhow!("Failed to parse contract JSON: {}", e))?;

    let spec = contract_data
        .get("spec")
        .ok_or_else(|| anyhow!("No spec found in contract metadata"))?;

    let target_function = if is_constructor {
        spec.get("constructor")
            .ok_or_else(|| anyhow!("No constructor found in contract metadata"))?
    } else {
        let function_name = function_name
            .ok_or_else(|| anyhow!("Function name required for non-constructor calls"))?;

        // Search in atomic_functions first
        if let Some(atomic_functions) =
            spec.get("atomic_functions").and_then(|f| f.as_array())
        {
            for func in atomic_functions {
                if func.get("label").and_then(|l| l.as_str()) == Some(function_name) {
                    return get_args_types(func);
                }
            }
        }

        // Search in readonly_functions
        if let Some(readonly_functions) =
            spec.get("readonly_functions").and_then(|f| f.as_array())
        {
            for func in readonly_functions {
                if func.get("label").and_then(|l| l.as_str()) == Some(function_name) {
                    return get_args_types(func);
                }
            }
        }

        return Err(anyhow!(
            "Function '{}' not found in contract metadata",
            function_name
        ));
    };

    get_args_types(target_function)
}

/// Extract argument types from a function definition
fn get_args_types(function_def: &Value) -> Result<Vec<String>> {
    let args = function_def
        .get("args")
        .and_then(|args| args.as_array())
        .ok_or_else(|| anyhow!("Invalid function args format"))?;

    let mut param_types = Vec::new();
    for arg in args {
        let type_name = arg
            .get("type")
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid parameter type"))?;
        param_types.push(type_name.to_string());
    }

    Ok(param_types)
}

/// Print function signature with metadata (label and type) for user guidance
fn print_function_signature_with_metadata(
    contract_file: &Path,
    is_constructor: bool,
    function_name: Option<&str>,
    output_json: bool,
) {
    if output_json {
        return; // Don't print signature info in JSON mode
    }

    match get_detailed_parameter_info(contract_file, is_constructor, function_name) {
        Ok(params) => {
            if is_constructor {
                println!("{}", "Constructor signature:".blue());
                if params.is_empty() {
                    println!("  {}()", "new".green());
                } else {
                    println!("  {}(", "new".green());
                    for (i, param) in params.iter().enumerate() {
                        let comma = if i == params.len() - 1 { "" } else { "," };
                        println!(
                            "    {}: {}{}",
                            param.label.cyan(),
                            param.type_name.yellow(),
                            comma
                        );
                    }
                    println!("  )");
                }
            } else if let Some(fname) = function_name {
                println!("Function '{}' signature:", fname.green());
                if params.is_empty() {
                    println!("  {}()", fname.green());
                } else {
                    println!("  {}(", fname.green());
                    for (i, param) in params.iter().enumerate() {
                        let comma = if i == params.len() - 1 { "" } else { "," };
                        println!(
                            "    {}: {}{}",
                            param.label.cyan(),
                            param.type_name.yellow(),
                            comma
                        );
                    }
                    println!("  )");
                }
            }
        }
        Err(_) => {
            println!("Unable to retrieve function signature from metadata");
        }
    }
}

/// Get detailed parameter information including labels and types
fn get_detailed_parameter_info(
    contract_file: &Path,
    is_constructor: bool,
    function_name: Option<&str>,
) -> Result<Vec<ConstructorParam>> {
    if is_constructor {
        return parse_constructor_metadata(contract_file);
    }

    let content = fs::read_to_string(contract_file)
        .map_err(|e| anyhow!("Failed to read contract file: {}", e))?;

    let contract_data: Value = serde_json::from_str(&content)
        .map_err(|e| anyhow!("Failed to parse contract JSON: {}", e))?;

    let spec = contract_data
        .get("spec")
        .ok_or_else(|| anyhow!("No spec found in contract metadata"))?;

    let function_name = function_name
        .ok_or_else(|| anyhow!("Function name required for non-constructor calls"))?;

    // Search in atomic_functions first
    if let Some(atomic_functions) =
        spec.get("atomic_functions").and_then(|f| f.as_array())
    {
        for func in atomic_functions {
            if func.get("label").and_then(|l| l.as_str()) == Some(function_name) {
                return get_detailed_args_info(func);
            }
        }
    }

    // Search in readonly_functions
    if let Some(readonly_functions) =
        spec.get("readonly_functions").and_then(|f| f.as_array())
    {
        for func in readonly_functions {
            if func.get("label").and_then(|l| l.as_str()) == Some(function_name) {
                return get_detailed_args_info(func);
            }
        }
    }

    Err(anyhow!(
        "Function '{}' not found in contract metadata",
        function_name
    ))
}

/// Extract detailed argument information from function definition
fn get_detailed_args_info(function_def: &Value) -> Result<Vec<ConstructorParam>> {
    let args = function_def
        .get("args")
        .and_then(|args| args.as_array())
        .ok_or_else(|| anyhow!("Invalid function args format"))?;

    let mut params = Vec::new();
    for arg in args {
        let label = arg
            .get("label")
            .and_then(|l| l.as_str())
            .ok_or_else(|| anyhow!("Invalid parameter label"))?;
        let type_name = arg
            .get("type")
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid parameter type"))?;

        params.push(ConstructorParam {
            label: label.to_string(),
            type_name: type_name.to_string(),
        });
    }

    Ok(params)
}

/// Convert a pre-parsed value to the expected type if necessary
fn convert_to_expected_type(value: Value, expected_type: &str) -> Result<Value> {
    match expected_type {
        "String" => {
            // For String type, convert any value to string
            match value {
                Value::String(s) => Ok(Value::String(s)),
                Value::Number(n) => Ok(Value::String(n.to_string())),
                Value::Bool(b) => Ok(Value::String(b.to_string())),
                _ => Ok(Value::String(value.to_string())),
            }
        }
        "i64" | "i32" | "i16" | "i8" | "u64" | "u32" | "u16" | "u8" => {
            // For integer types, ensure we have a number
            match value {
                Value::Number(n) => Ok(Value::Number(n)),
                Value::String(s) => {
                    if let Ok(int_val) = s.parse::<i64>() {
                        Ok(Value::Number(serde_json::Number::from(int_val)))
                    } else {
                        Err(anyhow!("Cannot convert '{}' to {}", s, expected_type))
                    }
                }
                _ => Err(anyhow!("Cannot convert {:?} to {}", value, expected_type)),
            }
        }
        "bool" => {
            // For boolean types, ensure we have a boolean
            match value {
                Value::Bool(b) => Ok(Value::Bool(b)),
                Value::String(s) => match s.to_lowercase().as_str() {
                    "true" | "1" => Ok(Value::Bool(true)),
                    "false" | "0" => Ok(Value::Bool(false)),
                    _ => Err(anyhow!("Cannot convert '{}' to boolean", s)),
                },
                Value::Number(n) => {
                    if let Some(i) = n.as_i64() {
                        Ok(Value::Bool(i != 0))
                    } else {
                        Err(anyhow!("Cannot convert number to boolean"))
                    }
                }
                _ => Err(anyhow!("Cannot convert {:?} to boolean", value)),
            }
        }
        _ => {
            // For complex types, return as-is
            Ok(value)
        }
    }
}

/// Parse constructor metadata from .contract file
fn parse_constructor_metadata(contract_file: &Path) -> Result<Vec<ConstructorParam>> {
    let content = fs::read_to_string(contract_file)
        .map_err(|e| anyhow!("Failed to read contract file: {}", e))?;

    let contract_data: Value = serde_json::from_str(&content)
        .map_err(|e| anyhow!("Failed to parse contract JSON: {}", e))?;

    let constructor = contract_data
        .get("spec")
        .and_then(|spec| spec.get("constructor"))
        .ok_or_else(|| anyhow!("No constructor found in contract metadata"))?;

    let args = constructor
        .get("args")
        .and_then(|args| args.as_array())
        .ok_or_else(|| anyhow!("Invalid constructor args format"))?;

    let mut params = Vec::new();
    for arg in args {
        let label = arg
            .get("label")
            .and_then(|l| l.as_str())
            .ok_or_else(|| anyhow!("Invalid parameter label"))?;
        let type_name = arg
            .get("type")
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid parameter type"))?;

        params.push(ConstructorParam {
            label: label.to_string(),
            type_name: type_name.to_string(),
        });
    }

    Ok(params)
}

/// Parse typed values with comprehensive type detection
pub fn parse_typed_value(input: &str) -> Result<Value, String> {
    let trimmed = input.trim();

    // Try parsing as different types in order of specificity

    // 1. Boolean literals
    if let Ok(value) = parse_bool_typed(trimmed) {
        return Ok(value);
    }

    // 2. Typed integers (u8, i32, etc.)
    if let Ok(value) = parse_typed_integer(trimmed) {
        return Ok(value);
    }

    // 3. Plain integers
    if let Ok(value) = parse_integer_typed(trimmed) {
        return Ok(value);
    }

    // 4. Floating point numbers
    if let Ok(value) = parse_float_typed(trimmed) {
        return Ok(value);
    }

    // 5. Quoted strings
    if let Ok(value) = parse_string_typed(trimmed) {
        return Ok(value);
    }

    // 6. Arrays
    if let Ok(value) = parse_array_typed(trimmed) {
        return Ok(value);
    }

    // 7. JSON objects and other complex types
    if let Ok(value) = parse_json_typed(trimmed) {
        return Ok(value);
    }

    // Default: treat as unquoted string
    Ok(Value::String(trimmed.to_string()))
}

/// Parse boolean literals: true, false
fn parse_bool_typed(input: &str) -> Result<Value, String> {
    match input.to_lowercase().as_str() {
        "true" => Ok(Value::Bool(true)),
        "false" => Ok(Value::Bool(false)),
        _ => Err("Not a boolean".to_string()),
    }
}

/// Parse typed integers: 42u8, -123i32, etc.
fn parse_typed_integer(input: &str) -> Result<Value, String> {
    // Match patterns like: 42u8, -123i32, 1000u64, etc.
    let patterns = [
        ("u8", false),
        ("u16", false),
        ("u32", false),
        ("u64", false),
        ("u128", false),
        ("i8", true),
        ("i16", true),
        ("i32", true),
        ("i64", true),
        ("i128", true),
        ("usize", false),
        ("isize", true),
    ];

    for (suffix, is_signed) in &patterns {
        if input.ends_with(suffix) {
            let number_part = &input[..input.len() - suffix.len()];

            if *is_signed {
                if let Ok(parsed) = number_part.parse::<i64>() {
                    return Ok(Value::Number(parsed.into()));
                }
            } else if let Ok(parsed) = number_part.parse::<u64>() {
                return Ok(Value::Number(parsed.into()));
            }

            // For i128/u128, fall back to string representation if too large
            if *suffix == "i128" || *suffix == "u128" {
                // Try to parse as string for very large numbers
                if number_part.parse::<f64>().is_ok() {
                    return Ok(Value::String(input.to_string()));
                }
            }

            return Err(format!("Invalid {} number: {}", suffix, number_part));
        }
    }

    Err("Not a typed integer".to_string())
}

/// Parse plain integers (no type suffix)
fn parse_integer_typed(input: &str) -> Result<Value, String> {
    if let Ok(parsed) = input.parse::<i64>() {
        Ok(Value::Number(parsed.into()))
    } else {
        Err("Not an integer".to_string())
    }
}

/// Parse floating point numbers: 3.14, -2.5, 1e10
fn parse_float_typed(input: &str) -> Result<Value, String> {
    if let Ok(parsed) = input.parse::<f64>() {
        if let Some(num) = serde_json::Number::from_f64(parsed) {
            Ok(Value::Number(num))
        } else {
            Err("Invalid float number".to_string())
        }
    } else {
        Err("Not a float".to_string())
    }
}

/// Parse quoted strings: "hello", 'world'
fn parse_string_typed(input: &str) -> Result<Value, String> {
    if (input.starts_with('"') && input.ends_with('"'))
        || (input.starts_with('\'') && input.ends_with('\''))
    {
        let content = &input[1..input.len() - 1];
        // Handle basic escape sequences
        let unescaped = unescape_string_typed(content);
        Ok(Value::String(unescaped))
    } else {
        Err("Not a quoted string".to_string())
    }
}

/// Basic string unescaping
fn unescape_string_typed(input: &str) -> String {
    input
        .replace("\\\"", "\"")
        .replace("\\'", "'")
        .replace("\\\\", "\\")
        .replace("\\n", "\n")
        .replace("\\r", "\r")
        .replace("\\t", "\t")
}

/// Parse arrays: [1, 2, 3], ["a", "b"]
fn parse_array_typed(input: &str) -> Result<Value, String> {
    if input.starts_with('[') && input.ends_with(']') {
        // Use serde_json to parse the array
        if let Ok(value) = serde_json::from_str::<Value>(input) {
            if value.is_array() {
                return Ok(value);
            }
        }
    }
    Err("Not an array".to_string())
}

/// Parse JSON objects and other complex types
fn parse_json_typed(input: &str) -> Result<Value, String> {
    if let Ok(value) = serde_json::from_str::<Value>(input) {
        Ok(value)
    } else {
        Err("Not valid JSON".to_string())
    }
}

#[cfg(test)]
mod type_parser_tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_parse_bool() {
        assert_eq!(parse_typed_value("true").unwrap(), json!(true));
        assert_eq!(parse_typed_value("false").unwrap(), json!(false));
    }

    #[test]
    fn test_parse_typed_integers() {
        assert_eq!(parse_typed_value("42u8").unwrap(), json!(42));
        assert_eq!(parse_typed_value("1000u16").unwrap(), json!(1000));
        assert_eq!(parse_typed_value("100000u32").unwrap(), json!(100000));
        assert_eq!(parse_typed_value("10000000u64").unwrap(), json!(10000000));

        assert_eq!(parse_typed_value("-42i8").unwrap(), json!(-42));
        assert_eq!(parse_typed_value("-1000i16").unwrap(), json!(-1000));
        assert_eq!(parse_typed_value("-100000i32").unwrap(), json!(-100000));
    }

    #[test]
    fn test_parse_strings() {
        assert_eq!(parse_typed_value("\"hello\"").unwrap(), json!("hello"));
        assert_eq!(parse_typed_value("'world'").unwrap(), json!("world"));
    }

    #[test]
    fn test_parse_arrays() {
        assert_eq!(parse_typed_value("[1, 2, 3]").unwrap(), json!([1, 2, 3]));
        assert_eq!(
            parse_typed_value("[\"a\", \"b\"]").unwrap(),
            json!(["a", "b"])
        );
    }

    #[test]
    fn test_fallback_to_string() {
        assert_eq!(
            parse_typed_value("unquoted_string").unwrap(),
            json!("unquoted_string")
        );
    }
}
