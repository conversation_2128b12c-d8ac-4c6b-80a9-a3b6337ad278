// Output formatting functionality for cargo-vcontract
// This module handles formatting of success and error outputs


use serde_json::{json, Value};

/// Format output as JSON or pretty-printed
pub fn format_output(value: Value, json_output: bool) -> String {
    if json_output {
        serde_json::to_string(&value).unwrap_or_else(|_| "Invalid JSON".to_string())
    } else {
        serde_json::to_string_pretty(&value)
            .unwrap_or_else(|_| "Invalid JSON".to_string())
    }
}

/// Format error as JSON or plain text
pub fn format_error(error_msg: &str, json_output: bool) -> String {
    if json_output {
        let error_obj = json!({
            "error": error_msg,
            "success": false
        });
        serde_json::to_string(&error_obj)
            .unwrap_or_else(|_| format!(r#"{{"error":"{}","success":false}}"#, error_msg))
    } else {
        error_msg.to_string()
    }
}

/// Format function validation errors with additional context
#[allow(dead_code)]
pub fn format_validation_error(
    error_msg: &str,
    contract_address: &str,
    function_name: &str,
    command_type: &str,
    json_output: bool,
) -> String {
    if json_output {
        let error_obj = json!({
            "error": error_msg,
            "error_type": "function_validation_error",
            "contract_address": contract_address,
            "function_name": function_name,
            "command_type": command_type,
            "success": false
        });
        serde_json::to_string(&error_obj)
            .unwrap_or_else(|_| format!(r#"{{"error":"{}","success":false}}"#, error_msg))
    } else {
        error_msg.to_string()
    }
}



/// Pretty format JSON result for user-friendly display
pub fn pretty_format_result(result: &Value) -> String {
    match result {
        Value::Null => "null".to_string(),
        Value::Bool(b) => {
            if *b {
                "true".to_string()
            } else {
                "false".to_string()
            }
        }
        Value::Number(n) => n.to_string(),
        Value::String(s) => format!("\"{}\"", s),
        Value::Array(arr) => {
            if arr.is_empty() {
                "[]".to_string()
            } else if arr.len() == 1 {
                pretty_format_result(&arr[0])
            } else {
                let items: Vec<String> = arr.iter().map(pretty_format_result).collect();
                format!("[{}]", items.join(", "))
            }
        }
        Value::Object(obj) => {
            if obj.is_empty() {
                "{}".to_string()
            } else {
                let items: Vec<String> = obj
                    .iter()
                    .map(|(k, v)| format!("{}: {}", k, pretty_format_result(v)))
                    .collect();
                format!("{{{}}}", items.join(", "))
            }
        }
    }
}
