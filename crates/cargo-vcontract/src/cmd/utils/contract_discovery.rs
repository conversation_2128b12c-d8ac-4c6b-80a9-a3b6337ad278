// Contract Discovery Service - Unified contract file discovery
// Eliminates code duplication across call, deploy, query, upgrade, and fork commands

use anyhow::{anyhow, Result};
use colored::Colorize;
use glob;
use std::fs;
use std::path::PathBuf;

/// Supported contract file types
#[derive(Debug, Clone, PartialEq)]
pub enum FileType {
    Contract, // .contract files only
    #[allow(dead_code)]
    Wasm, // .wasm files only (future extension)
    Both,     // both .contract and .wasm files
}

/// Search strategy for file discovery
#[derive(Debug, Clone, PartialEq)]
pub enum SearchStrategy {
    NewestFirst, // Sort by modification time, newest first (call/deploy/query/upgrade behavior)
    FirstFound,  // Return first file found (fork behavior)
    #[allow(dead_code)]
    AllMatches, // Return all matching files (future extension)
}

/// Configuration for contract file search
#[derive(Debug, Clone)]
pub struct SearchConfig {
    pub file_types: FileType,
    pub strategy: SearchStrategy,
    pub search_paths: Vec<String>,
    pub verbose: bool,
    pub require_metadata: bool, // true for call/query (reject .wasm), false for deploy/upgrade
}

impl SearchConfig {
    /// Create a new SearchConfig with default values
    pub fn new() -> Self {
        Self {
            file_types: FileType::Both,
            strategy: SearchStrategy::NewestFirst,
            search_paths: vec!["./".to_string()],
            verbose: false,
            require_metadata: false,
        }
    }

    /// Set file types to search for
    pub fn file_types(mut self, file_types: FileType) -> Self {
        self.file_types = file_types;
        self
    }

    /// Set search strategy
    pub fn strategy(mut self, strategy: SearchStrategy) -> Self {
        self.strategy = strategy;
        self
    }

    /// Set search paths
    pub fn search_paths(mut self, paths: Vec<String>) -> Self {
        self.search_paths = paths;
        self
    }

    /// Enable verbose output
    pub fn verbose(mut self, verbose: bool) -> Self {
        self.verbose = verbose;
        self
    }

    /// Set metadata requirement
    pub fn require_metadata(mut self, require: bool) -> Self {
        self.require_metadata = require;
        self
    }
}

/// Unified contract discovery service
pub struct ContractDiscoveryService;

impl ContractDiscoveryService {
    /// Find contract file with custom configuration
    pub fn find_contract_file(config: SearchConfig) -> Result<PathBuf> {
        let current_dir = std::env::current_dir()?;

        // Build search patterns based on configuration
        let mut patterns = Vec::new();

        for base_path in &config.search_paths {
            let path = if base_path == "./" {
                current_dir.clone()
            } else {
                current_dir.join(base_path)
            };

            match config.file_types {
                FileType::Contract => {
                    patterns.push(path.join("*.contract"));
                }
                FileType::Wasm => {
                    patterns.push(path.join("*.wasm"));
                }
                FileType::Both => {
                    patterns.push(path.join("*.contract"));
                    patterns.push(path.join("*.wasm"));
                }
            }
        }

        if config.verbose {
            println!("{}", "Searching for contract files...".blue());
        }

        // Search for files matching patterns
        let mut found_files = Vec::new();

        for pattern in patterns {
            let pattern_str = pattern.to_string_lossy().to_string();

            if config.verbose {
                println!("  Checking {}", pattern_str.blue());
            }

            if let Ok(entries) = glob::glob(&pattern_str) {
                for entry in entries.filter_map(Result::ok) {
                    found_files.push(entry);
                }
            }
        }

        if found_files.is_empty() {
            return Err(anyhow!("Contract file not found. Run 'cargo vcontract build' first to compile your contract."));
        }

        // Apply search strategy
        let selected_file = match config.strategy {
            SearchStrategy::FirstFound => found_files.into_iter().next().unwrap(),
            SearchStrategy::NewestFirst => {
                // Sort by modification time (newest first)
                let mut files_with_time: Vec<(PathBuf, std::time::SystemTime)> =
                    found_files
                        .into_iter()
                        .filter_map(|path| {
                            fs::metadata(&path)
                                .ok()
                                .and_then(|meta| meta.modified().ok())
                                .map(|time| (path, time))
                        })
                        .collect();

                files_with_time.sort_by(|a, b| b.1.cmp(&a.1));

                if let Some((path, _)) = files_with_time.first() {
                    path.clone()
                } else {
                    return Err(anyhow!("Contract file not found. Run 'cargo vcontract build' first to compile your contract."));
                }
            }
            SearchStrategy::AllMatches => {
                // For future use - currently return first
                found_files.into_iter().next().unwrap()
            }
        };

        // Check metadata requirements (for call/query commands)
        if config.require_metadata
            && selected_file.extension().and_then(|ext| ext.to_str()) == Some("wasm")
        {
            return Err(anyhow!("Found .wasm file: {}\n\nThe 'ls' command requires a .contract file with metadata.\n.wasm files don't contain function metadata needed for listing.\n\nPlease use a .contract file or build one with 'cargo vcontract build'.", selected_file.display()));
        }

        if config.verbose {
            println!(
                "  {}: {}",
                "Found contract file".green(),
                selected_file.display()
            );
        }

        Ok(selected_file)
    }

    /// Find contract file for call command (only .contract files, newest first)
    pub fn find_for_call(verbose: bool) -> Result<PathBuf> {
        let config = SearchConfig::new()
            .file_types(FileType::Contract)
            .strategy(SearchStrategy::NewestFirst)
            .search_paths(vec![
                "./".to_string(),
                "target/contract/".to_string(),
                "target/glue/".to_string(),
            ])
            .verbose(verbose)
            .require_metadata(true);

        Self::find_contract_file(config)
    }

    /// Find contract file for deploy command (.contract and .wasm files, newest first)
    pub fn find_for_deploy(verbose: bool) -> Result<PathBuf> {
        let config = SearchConfig::new()
            .file_types(FileType::Both)
            .strategy(SearchStrategy::NewestFirst)
            .search_paths(vec![
                "./".to_string(),
                "target/contract/".to_string(),
                "target/glue/".to_string(),
                "target/wasm32-unknown-unknown/release/".to_string(),
                "target/wasm32-unknown-unknown/debug/".to_string(),
            ])
            .verbose(verbose)
            .require_metadata(false);

        Self::find_contract_file(config)
    }

    /// Find contract file for query command (only .contract files, newest first)
    #[allow(dead_code)]
    pub fn find_for_query(verbose: bool) -> Result<PathBuf> {
        let config = SearchConfig::new()
            .file_types(FileType::Contract)
            .strategy(SearchStrategy::NewestFirst)
            .search_paths(vec![
                "./".to_string(),
                "target/contract/".to_string(),
                "target/glue/".to_string(),
            ])
            .verbose(verbose)
            .require_metadata(true);

        Self::find_contract_file(config)
    }

    /// Find contract file for upgrade command (.contract and .wasm files, newest first)
    #[allow(dead_code)]
    pub fn find_for_upgrade(verbose: bool) -> Result<PathBuf> {
        let config = SearchConfig::new()
            .file_types(FileType::Both)
            .strategy(SearchStrategy::NewestFirst)
            .search_paths(vec![
                "./".to_string(),
                "target/contract/".to_string(),
                "target/glue/".to_string(),
                "target/wasm32-unknown-unknown/release/".to_string(),
                "target/wasm32-unknown-unknown/debug/".to_string(),
            ])
            .verbose(verbose)
            .require_metadata(false);

        Self::find_contract_file(config)
    }

    /// Find contract file for fork command (only .contract files, first found)
    pub fn find_for_fork(_verbose: bool) -> Option<PathBuf> {
        let config = SearchConfig::new()
            .file_types(FileType::Contract)
            .strategy(SearchStrategy::FirstFound)
            .search_paths(vec![
                "./".to_string(),
                "target/glue/".to_string(),
            ])
            .verbose(false)  // fork doesn't use verbose output
            .require_metadata(false);

        Self::find_contract_file(config).ok()
    }
}
