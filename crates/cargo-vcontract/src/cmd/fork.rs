

use crate::services::{ServiceError, ServiceResult};
use clap::Args;
use colored::Colorize;
use contract_build::VerbosityFlags;
use serde_json::json;
use std::path::PathBuf;
use tokio::runtime::Runtime;
use vgraph_sdk_core::wallet::Wallet;

use crate::cmd::auth::get_saved_private_key;
use crate::cmd::config::get_node_url;
use crate::cmd::utils::{
    connect_node, display_constructor_arguments, enhanced_rpc_error_handler,
    format_output, parse_args_with_metadata, parse_json_args, validate_constructor_args,
    wait_for_confirmation, ContractDiscoveryService,
};

/// Fork a smart contract to create a new branch
#[derive(Debug, Args)]
#[clap(name = "fork")]
pub struct ForkCommand {
    /// Original contract to fork (code hash or contract address)
    #[clap(value_parser)]
    code_hash_or_address: String,

    /// Node URL (override config file setting - currently configured: use 'cargo vcontract config' to view/change)
    #[clap(long)]
    node: Option<String>,

    /// Contract initialization parameters (JSON format)
    #[clap(long, short = 'a', num_args = 0..)]
    args: Vec<String>,

    /// Make contract upgradable
    #[clap(long, default_value = "true")]
    upgradable: bool,

    /// Source code URL
    #[clap(long, default_value = "")]
    source_url: String,

    /// Git commit hash
    #[clap(long, default_value = "")]
    git_hash: String,

    /// Use reproducible build
    #[clap(long, default_value = "true")]
    reproducible: bool,

    /// Fuel value
    #[clap(long, default_value = "1000000")]
    fuel: u64,

    /// Verbosity flags
    #[clap(flatten)]
    verbosity: VerbosityFlags,
}

impl ForkCommand {
    /// Check if JSON output is requested
    pub fn is_json(&self) -> bool {
        self.verbosity.is_json()
    }

    /// Auto-detect and find corresponding .contract file for enhanced parameter validation
    fn find_contract_file(&self, _code_hash: &str) -> Option<PathBuf> {
        ContractDiscoveryService::find_for_fork(false)
    }

    pub fn exec(&self) -> ServiceResult<()> {
        // Create tokio runtime
        let runtime = Runtime::new()
            .map_err(|e| ServiceError::internal(format!("Failed to create tokio runtime: {}", e)))?;
        runtime.block_on(self.async_exec())
    }

    async fn async_exec(&self) -> ServiceResult<()> {
        let verbose = self.verbosity.is_verbose();

        // Get private key from saved credentials
        let private_key = get_saved_private_key()?;

        // Create wallet from private key
        if verbose {
            println!("{}", "Creating wallet from saved private key...".blue());
        }

        let mut wallet = Wallet::new();
        let private_key_clean = private_key.trim_start_matches("0x");
        let private_key_bytes = hex::decode(private_key_clean)
            .map_err(|e| anyhow!("Invalid saved private key format: {}", e))?;

        wallet
            .import_keypair("default".to_string(), private_key_bytes)
            .map_err(|e| anyhow!("Failed to import saved private key: {}", e))?;

        // Connect to node
        let node_url = get_node_url(self.node.as_deref().unwrap_or("127.0.0.1:9877"))?;
        let client = connect_node(&node_url, self.verbosity.is_json()).await?;

        // Get code hash from input (contract address or code hash)
        let input_code_hash = &self.code_hash_or_address;

        // Handle both code hash and contract address cases
        let input_with_prefix = if !input_code_hash.starts_with("0x") {
            format!("0x{}", input_code_hash)
        } else {
            input_code_hash.clone()
        };

        // Try to get code hash from contract address first
        let final_code_hash = if input_with_prefix.len() == 66 {
            // 0x + 64 hex chars
            // Try as contract address first
            match client
                .request("contract.get_code_hash", Some(json!([&input_with_prefix])))
                .await
            {
                Ok(response) => {
                    if let Some(hash) = response.get("code_hash").and_then(|v| v.as_str())
                    {
                        if verbose {
                            println!(
                                "Retrieved code hash from contract address: {}",
                                hash.green()
                            );
                        }
                        hash.to_string()
                    } else {
                        // Assume it's already a code hash
                        if verbose {
                            println!(
                                "Using input as code hash: {}",
                                input_with_prefix.green()
                            );
                        }
                        input_with_prefix
                    }
                }
                Err(_) => {
                    // Assume it's already a code hash
                    if verbose {
                        println!(
                            "Using input as code hash: {}",
                            input_with_prefix.green()
                        );
                    }
                    input_with_prefix
                }
            }
        } else {
            return Err(ServiceError::validation("Invalid input format. Expected 64-character hex string (code hash or contract address)".to_string()));
        };

        // Try to auto-detect .contract file for enhanced parameter validation
        let contract_file = self.find_contract_file(&final_code_hash);

        // Process arguments with enhanced validation if .contract file is found
        let constructor_args = if let Some(ref contract_file) = contract_file {
            if verbose {
                println!(
                    "Found .contract file: {} - enabling enhanced parameter validation",
                    contract_file.display().to_string().green()
                );
            }

            // Parse arguments with metadata support
            let args = parse_args_with_metadata(
                &self.args,
                contract_file,
                true, // is_constructor
                None, // function_name (not needed for constructor)
                self.verbosity.is_json(),
            )?;

            // Validate constructor arguments
            validate_constructor_args(
                &args,
                contract_file,
                verbose,
                self.verbosity.is_json(),
            )?;

            // Display constructor arguments before transaction
            if !self.verbosity.is_json() && !args.is_empty() {
                display_constructor_arguments(contract_file, &args)?;
            }

            args
        } else {
            if verbose {
                println!("No .contract file found - using smart argument parsing");
            }
            // Fallback to smart parsing (strings by default) without metadata
            let args = parse_json_args(&self.args)?;

            // Display constructor arguments even without .contract file
            if !self.verbosity.is_json() && !args.is_empty() {
                println!("{}:", "Constructor Arguments".green().bold());
                for (i, value) in args.iter().enumerate() {
                    println!(
                        "  {}: {}",
                        format!("arg{}", i).blue().bold(),
                        value.to_string().green()
                    );
                }
            }

            args
        };

        // Submit transaction using contract.fork RPC
        if verbose {
            println!("{}", "Submitting fork transaction to network...".blue());
        }
        // Show fuel usage only in non-JSON mode
        if !self.verbosity.is_json() {
            println!(
                "{} Using fuel: {}",
                "[FUEL]".yellow(),
                self.fuel.to_string().green()
            );
        }

        // Show the original code hash that we're forking from
        if !self.verbosity.is_json() {
            println!("Original Code Hash: {}", final_code_hash.green());
        }

        // Prepare parameters for contract.fork RPC call
        let params = json!([{
            "contract_code_hash": final_code_hash,
            "constructor_parameters": constructor_args,
            "contract_source_url": self.source_url.clone(),
            "upgradable": self.upgradable,
            "git_commit_hash": self.git_hash.clone(),
            "reproducible_build": self.reproducible,
            "fuel": self.fuel,
            "privatekey": private_key
        }]);

        // Call contract.fork RPC
        let response = match client.request("contract.fork", Some(params)).await {
            Ok(response) => response,
            Err(e) => {
                let error_msg = enhanced_rpc_error_handler(
                    &anyhow!("{}", e),
                    self.verbosity.is_json(),
                );
                if self.verbosity.is_json() {
                    println!("{}", error_msg);
                } else {
                    eprintln!("{}", error_msg);
                }
                return Ok(());
            }
        };

        // Extract transaction hash and new contract address
        let tx_hash = match response.get("transaction_hash").and_then(|v| v.as_str()) {
            Some(hash) => hash,
            None => {
                let error_msg = enhanced_rpc_error_handler(
                    &anyhow!(
                        "Missing transaction_hash in response. Full response: {}",
                        response
                    ),
                    self.verbosity.is_json(),
                );
                if self.verbosity.is_json() {
                    println!("{}", error_msg);
                } else {
                    eprintln!("{}", error_msg);
                }
                return Ok(());
            }
        };

        if verbose && !self.verbosity.is_json() {
            println!("Fork transaction submitted: {}", tx_hash.green());
        }

        // Wait for confirmation
        let receipt =
            wait_for_confirmation(&client, tx_hash, !self.verbosity.is_json()).await?;

        // Check if transaction failed first
        let transaction_success = receipt
            .get("receipt")
            .and_then(|r| r.get("status"))
            .and_then(|s| s.as_bool())
            .unwrap_or(false);

        if !transaction_success {
            // Transaction failed, extract error from op_result
            let error_message = receipt
                .get("receipt")
                .and_then(|r| r.get("op_result"))
                .and_then(|op| op.get("contract_address"))
                .and_then(|v| v.as_str())
                .unwrap_or("Transaction execution failed");

            let error_result = json!({
                "transaction_hash": tx_hash,
                "error": error_message,
                "original_code_hash": final_code_hash,
                "fuel_used": receipt.get("fuel_used").unwrap_or(&json!(0)),
                "receipt": receipt,
                "success": false
            });

            let output = format_output(error_result, self.verbosity.is_json());
            println!("{}", output);
            return Ok(());
        }

        // Extract new contract address from receipt
        let new_contract_address = receipt
            .get("contract_address")
            .and_then(|v| v.as_str())
            .or_else(|| {
                // Try alternative paths in receipt structure
                receipt
                    .get("receipt")
                    .and_then(|r| r.get("op_result"))
                    .and_then(|op| op.get("contract_address"))
                    .and_then(|v| v.as_str())
            })
            .ok_or_else(|| {
                if !self.verbosity.is_json() && verbose {
                    eprintln!("Full receipt structure:");
                    eprintln!(
                        "{}",
                        serde_json::to_string_pretty(&receipt).unwrap_or_default()
                    );
                }
                anyhow!("Failed to extract contract_address from receipt")
            })?;

        // Format and output result
        let result = json!({
            "transaction_hash": tx_hash,
            "new_contract_address": new_contract_address,
            "original_code_hash": final_code_hash,
            "fuel_used": receipt.get("fuel_used").unwrap_or(&json!(0)),
            "receipt": receipt,
            "success": true
        });

        let output = format_output(result, self.verbosity.is_json());

        if self.verbosity.is_json() {
            println!("{}", output);
        } else {
            println!();
            println!("{}: {}", "Transaction Hash".green().bold(), tx_hash);
            println!(
                "{}: {}",
                "New Contract Address".green().bold(),
                new_contract_address
            );
            println!(
                "{}: {}",
                "Original Code Hash".green().bold(),
                final_code_hash
            );
            if let Some(fuel_used) = receipt.get("fuel_used").and_then(|f| f.as_u64()) {
                if fuel_used > 0 {
                    println!(
                        "{} {}: {}",
                        "[GAS]".yellow(),
                        "Fuel Used".green().bold(),
                        fuel_used.to_string().yellow()
                    );
                }
            }
            if verbose {
                println!("{}:", "Receipt".green().bold());
                let pretty_receipt = serde_json::to_string_pretty(&receipt)
                    .unwrap_or_else(|_| receipt.to_string());
                println!("{}", pretty_receipt);
            }
            println!();
            println!("{}", "Fork contract successful!".green().bold());
        }

        Ok(())
    }
}
