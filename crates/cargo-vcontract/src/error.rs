use crate::services::ServiceError;
use serde_json::json;

/// Central error handling function that formats output based on JSON flag
pub fn handle_error(err: &ServiceError, is_json: bool) -> ! {
    if is_json {
        println!(
            "{}",
            json!({
                "error": true,
                "message": err.to_string(),
                "type": match err {
                    ServiceError::Network { .. } => "NetworkError",
                    ServiceError::Validation { .. } => "ValidationError",
                    ServiceError::Config { .. } => "ConfigError",
                    ServiceError::Auth { .. } => "AuthError",
                    ServiceError::Metadata { .. } => "MetadataError",
                    ServiceError::Contract { .. } => "ContractError",
                    ServiceError::FileSystem { .. } => "FileSystemError",
                    ServiceError::Internal { .. } => "InternalError",
                }
            })
        );
    } else {
        eprintln!("Error: {}", err);
    }
    std::process::exit(1);
}

/// Check if a command requires JSON output
pub fn check_is_json(cmd: &crate::Command) -> bool {
    use crate::Command;
    match cmd {
        Command::Build(build) => build.is_json(),
        Command::Call(call) => call.is_json(),
        Command::Deploy(deploy) => deploy.is_json(),
        Command::Query(query) => query.is_json(),
        Command::Upgrade(upgrade) => upgrade.is_json(),
        Command::Fork(fork) => fork.is_json(),
        Command::Receipt(receipt) => receipt.is_json(),
        // KeyCommand and ConfigCommand don't have verbosity flags
        Command::Key(_) => false,
        Command::Config(_) => false,
        // Commands without verbosity flags default to false
        Command::New { .. } | Command::Version => false,
    }
}